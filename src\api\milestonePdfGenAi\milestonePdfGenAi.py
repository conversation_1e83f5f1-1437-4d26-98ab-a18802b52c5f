import json
import pymupdf4llm
import google.generativeai as genai
from pymongo import MongoClient
import boto3
import os


def lambda_handler(event, context):
    # Configure Google GenAI
    genai.configure(api_key="AIzaSyAn2l1eGXPMUKw5VObUZp17jonRLDrjt9g")
    model = genai.GenerativeModel(model_name="gemini-1.5-flash")

    # MongoDB connection
    client = MongoClient(
        "mongodb://mongdbuser:%266XR%24a%25jS2W5z%5E%40%24@***********:27017/admin"
    )
    db = client["Fasst"]
    collection = db["PropFromBridge"]

    # Get the address key and S3 file details from the API request
    print(event)
    data = event["data"]
    address = data.get("address")

    if not address:
        return {
            "statusCode": 400,
            "body": json.dumps({"error": "Missing 'address' in request."}),
        }

    # Check if the data for this address already exists in the database
    existing_entry = collection.find_one({"address": address})
    collection.update_one(
        {"_id": existing_entry["_id"]}, {"$set": {"status.milestonePdfGenAi.status": 1}}
    )
    s3_client = boto3.client("s3")

    if existing_entry.get("mileStoneReportText") is not None:
        # If already present, return the stored AI response
        pdf_text = existing_entry["mileStoneReportText"]
    else:
        # Download the PDF file from S3
        local_pdf_path = f"/tmp/{existing_entry['milestoneReportKey']}"
        try:
            s3_client.download_file(
                "fasstcondo-public-files",
                existing_entry["milestoneReportKey"],
                local_pdf_path,
            )
        except Exception as e:
            collection.update_one(
                {"_id": existing_entry["_id"]},
                {
                    "$set": {
                        "status.milestonePdfGenAi.status": 3,
                        "status.milestonePdfGenAi.error": str(e),
                    }
                },
            )

            return {
                "statusCode": 500,
                "body": json.dumps(
                    {"error": "Failed to download PDF from S3.", "details": str(e)}
                ),
            }
        try:
            pdf_text = pymupdf4llm.to_markdown(local_pdf_path)
            collection.update_one(
                {"address": address}, {"$set": {"mileStoneReportText": pdf_text}}
            )

            # Clean up temporary file
            if os.path.exists(local_pdf_path):
                os.remove(local_pdf_path)
        except Exception as e:
            collection.update_one(
                {"_id": existing_entry["_id"]},
                {
                    "$set": {
                        "status.milestonePdfGenAi.status": 3,
                        "status.SIRSPdfGenAi.error": str(e),
                    }
                },
            )

    # Send to Generative AI API
    prompt = "Is a phase 2 milestone report needed? (Answer Yes or No), Is there any area of substantial deterioration and/or dangerous or unsafe conditions? (Yes   or No)  output as json,output keys=phase2MilestoneReportNeeded, substantialDeteriorationUnsafeConditions "
    try:
        response = model.generate_content([prompt, pdf_text])
        print(response)
    except Exception as e:
        collection.update_one(
            {"_id": existing_entry["_id"]},
            {"status.milestonePdfGenAi.status": 3, "status.milestonePdfGenAi.error": str(e)},
        )

    # Extract JSON result from AI response
    ai_response = {}
    try:
        if "```JSON" in response.text:
            ai_response = json.loads(response.text.strip().strip("```JSON").strip())
        else:
            ai_response = json.loads(response.text.strip().strip("```json").strip())
    except json.JSONDecodeError as e:
        collection.update_one(
            {"_id": existing_entry["_id"]},
            {
                "$set": {
                    "status.milestonePdfGenAi.status": 3,
                    "status.milestonePdfGenAi.error": str(e),
                }
            },
        )
        return {
            "statusCode": 500,
            "body": json.dumps(
                {"error": "Failed to parse AI response as JSON.", "details": str(e)}
            ),
        }

    # Save the result to MongoDB
    collection.update_one(
        {"address": address},
        {
            "$set": {
                "milestoneAiResponse": ai_response,
                "status.milestonePdfGenAi.status": 2,
            }
        },
    )
