import json
import boto3
import logging
import os

logger = logging.getLogger()
logger.setLevel(logging.INFO)

def handler(event, context):
    common_headers = {
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "OPTIONS,POST",
    }
    cognito = boto3.client('cognito-idp')
    
    try:
    
        body = json.loads(event['body'])
        username = body['username']
        password = body['password']
        
        logger.info(f"Attempting login for user: {username}")
        user_pool_id = os.environ['USER_POOL_ID']
        client_id = os.environ['USER_POOL_CLIENT_ID']
        
    
        user_info = cognito.admin_get_user(
            UserPoolId=user_pool_id,
            Username=username
        )
        
        
        email_verified = next((attr['Value'] for attr in user_info['UserAttributes'] if attr['Name'] == 'email_verified'), None)
        
        if email_verified == 'true':
        
            logger.info(f"User {username} is confirmed. Attempting authentication.")
            response = cognito.initiate_auth(
                ClientId=client_id,
                AuthFlow='USER_PASSWORD_AUTH',
                AuthParameters={
                    'USERNAME': username,
                    'PASSWORD': password
                }
            )
            
            logger.info(f"Authentication response: {json.dumps(response)}")
            
            if 'AuthenticationResult' in response:
                return {
                    'statusCode': 200,
                    'body': json.dumps({
                        'message': 'Login successful',
                        'token': response['AuthenticationResult']['IdToken']
                    }),
             "headers": common_headers,
                    
                }
            else:
                logger.error(f"Unexpected response: {json.dumps(response)}")
                return {
                    'statusCode': 500,
                    'body': json.dumps('Unexpected authentication response'),
             "headers": common_headers,
                    
                }
        else:
            logger.warning(f"User {username} is not confirmed.")
            return {
                'statusCode': 403,
                'body': json.dumps({
                    'message': 'User is not confirmed. Please check your email and confirm your account.'
                }),
             "headers": common_headers,
                
            }
    except cognito.exceptions.UserNotConfirmedException:
        logger.warning(f"User {username} is not confirmed.")
        return {
            'statusCode': 403,
            'body': json.dumps({
                'message': 'User is not confirmed. Please check your email and confirm your account.'
            }),
             "headers": common_headers,
            
        }
    except cognito.exceptions.NotAuthorizedException:
        logger.warning(f"Incorrect username or password for user: {username}")
        return {
            'statusCode': 401,
            'body': json.dumps('Incorrect username or password'),
             "headers": common_headers,
            
        }
    except cognito.exceptions.UserNotFoundException:
        logger.warning(f"User not found: {username}")
        return {
            'statusCode': 404,
            'body': json.dumps('User not found'),
             "headers": common_headers,
            
        }
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}", exc_info=True)
        return {
            'statusCode': 500,
            'body': json.dumps(f'An unexpected error occurred: {str(e)}'),
             "headers": common_headers,
            
        }
