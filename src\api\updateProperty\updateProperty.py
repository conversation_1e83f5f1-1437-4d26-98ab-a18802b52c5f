import json
import sys
import os
import logging
import traceback
import smtplib
from datetime import datetime
from pymongo import MongoClient
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from bson.objectid import ObjectId  

# Add the src directory to the Python path
sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)
from helper.db import connect_db

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger()

# Email configuration
SENDER_EMAIL = os.getenv("SENDER_EMAIL")
APP_PASSWORD = os.getenv("APP_PASSWORD")
RECIPIENT_EMAIL = os.getenv("RECIPIENT_EMAIL")


def send_email(property_address):
    """Send notification email about property update"""
    subject = "Property Update Successful"
    body = f"The property at {property_address} has been successfully updated."

    msg = MIMEMultipart()
    msg["From"] = SENDER_EMAIL
    msg["To"] = RECIPIENT_EMAIL
    msg["Subject"] = subject
    msg.attach(MIMEText(body, "plain"))

    try:
        with smtplib.SMTP("smtp.gmail.com", 587) as server:
            server.starttls()
            server.login(SENDER_EMAIL, APP_PASSWORD)
            server.sendmail(SENDER_EMAIL, RECIPIENT_EMAIL, msg.as_string())
        logger.info("Email sent successfully")
    except Exception as e:
        logger.error("Email failed: %s", e)


def handler(event, context):
    """Lambda handler for property updates"""
    common_headers = {
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "OPTIONS,POST",
    }

    if event.get("httpMethod") == "OPTIONS":
        return {
            "statusCode": 200,
            "body": json.dumps({"message": "CORS preflight"}),
            "headers": common_headers,
        }

    try:
        # Connect to MongoDB
        db = connect_db()
        collection = db["PropFromBridge"]
        logger.info("MongoDB connected")

        # Parse request body
        body = json.loads(event.get("body", "{}"))

        # Validate required fields
        if not body.get("_id"):
            logger.error("Missing property ID")
            return {
                "statusCode": 400,
                "body": json.dumps({"error": "Property ID required"}),
                "headers": common_headers,
            }

        try:
            property_filter = {"_id": ObjectId(body["_id"])}
        except Exception as e:
            logger.error("Invalid ID format: %s", body["_id"])
            return {
                "statusCode": 400,
                "body": json.dumps({"error": "Invalid property ID"}),
                "headers": common_headers,
            }

        sirs_ai_response = body.get("sirsAiResponse", {})
        milestone_ai_response = body.get("milestoneAiResponse", {})
        existing_property = collection.find_one(property_filter)

        if not existing_property:
            logger.warning("Property not found: %s", body["_id"])
            return {
                "statusCode": 404,
                "body": json.dumps({"error": "Property not found"}),
                "headers": common_headers,
            }

        # updated_building_elements = body.get("buildingElementData", existing_property.get("buildingElementData", []))
        # updated_building_services = body.get("buildingServicesData", existing_property.get("buildingServicesData", []))
        tableData = body.get("tableData", existing_property.get("tableData", {}))

        # Prepare update data
        update_fields = {
            "askingPrice": body.get("askingPrice"),
            "regularAssessmentAmount": body.get("regularAssessmentAmount"),
            "frequencyOfRegularAssessment": body.get("frequencyOfRegularAssessment"),
            "specialAssessmentAmount": body.get("specialAssessmentAmount"),
            "frequencyOfSpecialAssessment": body.get("frequencyOfSpecialAssessment"),
            "milestoneReportAvailable": body.get("milestoneReportAvailable"),
            "milestoneReportDueDate": body.get("milestoneReportDueDate"),
            "sirsReportAvailable": body.get("sirsReportAvailable"),
            "sirsReportDueDate": body.get("sirsReportDueDate"),
            "buyerAgentCompensation": body.get("buyerAgentCompensation"),
            "status.userRequest.status": "fasstUpdated",
            "sirsAiResponse": sirs_ai_response,
            "milestoneAiResponse": milestone_ai_response,
            # "buildingElementData": updated_building_elements,
            "tableData": tableData,
        }

        # Remove None values and empty dictionaries
        update_data = {k: v for k, v in update_fields.items() if v is not None and (not isinstance(v, dict) or v)}

        # Perform update
        result = collection.update_one(property_filter, {"$set": update_data})

        if result.matched_count == 0:
            logger.warning("Property not found: %s", body["_id"])
            return {
                "statusCode": 404,
                "body": json.dumps({"error": "Property not found"}),
                "headers": common_headers,
            }

        logger.info("Update successful: %s", update_data)

        # Send notification email
        send_email(body.get("address", "Unknown address"))

        # Ensure ObjectId is converted to a string before sending response
        updated_property = collection.find_one(property_filter)
        if updated_property:
            updated_property["_id"] = str(updated_property["_id"])  # Convert ObjectId to string

        return {
            "statusCode": 200,
            "body": json.dumps({
                "message": "Property updated",
                "status": True,
                "updatedFields": list(update_data.keys()),
                "updatedProperty": updated_property,  # Send updated document
            }, default=str),  # Convert ObjectId in case of serialization issues
            "headers": common_headers,
        }

    except Exception as e:
        logger.error("Update failed: %s", traceback.format_exc())
        return {
            "statusCode": 500,
            "body": json.dumps({"error": "Server error", "details": str(e)}),
            "headers": common_headers,
        }
