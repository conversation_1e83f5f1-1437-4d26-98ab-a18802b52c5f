import traceback
import os
import openpyxl
from openpyxl.styles import <PERSON><PERSON><PERSON>ill, Border, Side, Font, Alignment
from pymongo import MongoClient
from datetime import datetime, timedelta, timezone
from pathlib import Path
import boto3
import smtplib
from io import BytesIO
from botocore.exceptions import NoCredentialsError
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
import json
import re

# Configure Gemini AI

# AWS S3 configuration
AWS_BUCKET_NAME = "fasst-property-excel"
SENDER_EMAIL = "<EMAIL>"
SENDER_PASSWORD = "oddm nbuq esim jfux"
RECIPIENTS = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    
]
EMAIL_SUBJECT = "Excel file download link - Fasst Prop"
heading_row = 1  # Row where headers are located in the Excel file

# MongoDB connection
client = MongoClient(
    "mongodb://mongdbuser:%266XR%24a%25jS2W5z%5E%40%24@***********:27017/admin"
)
Property = client["Fasst"]
PropFromBridge = Property["PropFromBridge"]
PropertyAppraisal = Property["PropertyAppraisal"]




def get_days_ago(timestamp):
    now = datetime.now(timezone.utc)
    past_date = datetime.strptime(timestamp, "%Y-%m-%dT%H:%M:%S.%fZ").replace(
        tzinfo=timezone.utc
    )
    return (now - past_date).days


def sanitize_value(value):
    if isinstance(value, str):
        return "".join(char for char in value if ord(char) >= 32 or char in "\t\n\r")
    return value



def get_excel_template_path():
    """Get the path to the existing Excel template file"""
    current_directory = os.getcwd()
    excel_file_path = os.path.join(current_directory, "src/engine/workBook/FasstWorkBook.xlsx")

    if not os.path.exists(excel_file_path):
        raise FileNotFoundError(f"Template file not found at: {excel_file_path}")

    return excel_file_path


def handler(event, context):
    try:
        # Get path to existing template
        excel_file_path = get_excel_template_path()
        print(f"Using Excel template at: {excel_file_path}")

        # Load workbook
        workbook = openpyxl.load_workbook(excel_file_path)
        worksheet = workbook.active

        # Get date range
        if event.get("day") == "previousDay":
            day = datetime.now() - timedelta(days=1)

        if event.get("day") == "currentDay":
            day = datetime.now() - timedelta(days=0)

        # Get data
        data = find_today_data(day)
        print(f"Found {len(data)} records to process")

        # Clear existing data below headers
        for row in worksheet.iter_rows(min_row=heading_row + 1):
            for cell in row:
                cell.value = None

        # Write new data
        for row_index, row_data in enumerate(data, start=heading_row + 1):
            print(f"Processing row {row_index}: {row_data}")

            # Map data to columns
            columns_mapping = {
                1: "Source",
                2: "County",
                3: "Street",
                4:"City",
                5:"Unparsed Address",
                6:"Beds",
                7:"Baths",
                8:"Heated",
                9:"Waterfront",
                10:"Asking Price",
                11:"Asking PPSF",
                12:"Type",
                13:"Occupancy",
                14:"Status",
                15:"Year owned",
                16:"Last Sale Price",
                17:"Public comments",
                18:"Private comments",
                19:"Recommendation 2",
                20:"Permits",
                21:"Listing agent name",
                22:"Listing agent Cell",
                23:"Listing agent email",
                24:"Listing agent office",
                25:"Appraiser",
                26:"Building Sketch",
                
                
             
            }


            for col_num, field_name in columns_mapping.items():
                worksheet.cell(
                    row=row_index, column=col_num, value=row_data.get(field_name, "")
                )

        # Apply formatting
        apply_excel_formatting(worksheet, len(data))

        # Save to in-memory file
        excel_bytes_io = BytesIO()
        workbook.save(excel_bytes_io)
        excel_bytes_io.seek(0)

        # Upload to S3
        s3 = boto3.client("s3")
        file_name = f"Fasst_Workbook_Detailed_{day.date()}.xlsx"
        try:
            s3.upload_fileobj(excel_bytes_io, AWS_BUCKET_NAME, file_name)
            print(f"File uploaded successfully to S3: {file_name}")
        except NoCredentialsError:
            print("AWS credentials not available")
            raise

        # Generate download link
        download_link = generate_download_link(file_name, 604800)
        print(f"Generated download link: {download_link}")

      
        send_email(
    sender_email=SENDER_EMAIL,
    sender_password=SENDER_PASSWORD,
    recipients=RECIPIENTS,
    subject=EMAIL_SUBJECT,
    workbook_date=day,
    download_link=download_link,
    company_name="Fasst",
    logo_url = "https://habu-documents.s3.us-east-1.amazonaws.com/logo.png",
    cta_text="Download Workbook"
)

    except Exception as e:
        error = traceback.format_exc()
        
        print(f"Error in handler: {error}")
        raise


def apply_excel_formatting(worksheet, data_count):
    """Apply formatting to the Excel worksheet"""
    # Format currency columns
    currency_columns = [3, 4, 5]  # Rehab, High Cost, Low Cost columns (1-based index)
    for col in currency_columns:
        for row in range(heading_row + 1, heading_row + data_count + 1):
            cell = worksheet.cell(row=row, column=col)
            if (
                cell.value
                and isinstance(cell.value, str)
                and cell.value.startswith("$")
            ):
                cell.number_format = '"$"#,##0.00'

    # Set text wrapping for components and notes
    wrap_columns = [6, 7]
    for col in wrap_columns:
        for row in range(heading_row + 1, heading_row + data_count + 1):
            cell = worksheet.cell(row=row, column=col)
            cell.alignment = Alignment(wrapText=True, vertical="top")

    # Add borders to all cells
    thin_border = Border(
        left=Side(style="thin"),
        right=Side(style="thin"),
        top=Side(style="thin"),
        bottom=Side(style="thin"),
    )

    for row in worksheet.iter_rows(
        min_row=heading_row, max_row=heading_row + data_count
    ):
        for cell in row:
            cell.border = thin_border


def find_today_data(date):
    query = {
        "$or": [
            {
                "onMarketTimestamp": {"$regex": str(date.date())},
                "Source": "Stellar New",
            }
        ]
    }

    print(f"Executing query for date: {date.date()}")
    today_data = []
    try:
        properties = list(PropFromBridge.find(query))
        print(f"Found {len(properties)} properties in query")
        count = 1
        for property in properties:
            try:

                new_data = {}
                new_data["Street"] = property.get("addressStreet", "")


                Id = property["_id"]
                new_data["Source"] = property.get("Source", "N/A")
                new_data["County"] = property.get("County", "N/A")
                new_data["Unparsed Address"] = property.get("address", "N/A")
                
                new_data["Street"] = property.get("addressStreet", "N/A")
                new_data["City"] = property.get("addressCity", "N/A")
                new_data["UnparsedAddress"] = property.get("address", "N/A")
                new_data["Beds"] = property.get("beds", "N/A")
                new_data["Baths"] = property.get("bathroom", "N/A")
                new_data["Heated"] = property.get("heatedSpace", "N/A")
                new_data["Asking Price"] = property.get("askingPrice", "N/A")
                try:
                    new_data["Asking PPSF"] =new_data["Asking Price"] / new_data["Heated"] if new_data["Heated"] else "N/A"
                except ZeroDivisionError:
                    new_data["Asking PPSF"] = "N/A"
                new_data["Type"] = property.get("homeType", "N/A")
                new_data["Status"] = property.get("OccupantType", "N/A")
                # new_data["Year owned"] = property.get("year", "N/A")
                new_data["Public comments"] = property.get("description", "N/A")
                new_data["Private comments"] = property.get("taxDescription", "N/A")
                new_data["Waterfront"] = property.get("Waterfront", "N/A")
                new_data["Listing agent name"]=property.get("ListAgentNamePrefix", "N/A")
                new_data["Listing agent Cell"]=property.get("ListAgentPreferredPhone", "N/A")
                new_data["Listing agent email"]=property.get("ListAgentEmail", "N/A")
                new_data["Listing agent office"]=property.get("ListOfficeName", "N/A")
                new_data["Permits"]=str(property.get("entireAppartmnetPermitDetails","N/A"))
                property_appraisal=PropertyAppraisal.find_one({"propFromBridgeId":property['_id']})
                if property["status"]["crawlDetailedAppraisal"]["status"] == 2:
                        try:
                            property["Appraiser Mailing Address"] = property_appraisal[
                                "mailingAddress"
                            ]
                        except:
                            property["Appraiser Mailing Address"] = ""

                        try:
                            property["Appraiser site Address"] = property_appraisal["siteAddress"]
                        except:
                            property["Appraiser site Address"] = ""
                        if (
                            property["Appraiser Mailing Address"].lower()
                            == property["Appraiser site Address"].lower()
                        ):
                            new_data["Occupancy"] = "Owner"
                        else:
                            new_data["Occupancy"] = "Tenant"

                        property["Appraiser sales history"] = []

                        try:
                            sales = {}
                            for sales_history in property_appraisal["salesHistory"]:
                                try:
                                    if (
                                        sales_history["Price"] != "$100"
                                        and sales_history["Price"] != "$0"
                                    ):
                                        sales["sales date"] = sales_history["Sale Date"]
                                        sales["sales price"] = sales_history["Price"]
                                        break

                                except:
                                    sales["sales date"] = ""
                                    sales["sales price"] = ""
                                # pass
                            # try:
                            #     sales['sales price']=sales_history['salePrice']
                            # except:
                            #      sales['sales price']=""
                            #      pass
                            # property['Appraiser sales history'].append(sales)
                        except:
                            pass

                        try:
                            new_data["Last Sale Price"] = sales["sales price"]
                        except:
                            pass
                        try:
                            date_string = sales["sales date"]
                            date_object = datetime.strptime(date_string, "%d-%b-%Y")
                            year = date_object.year
                            new_data["Years owned"] = datetime.now().year - year
                        except:
                            pass
                        try:
                            land_dimensions = property_appraisal["landInformation"][0][
                                "Land Dimensions"
                            ].split("x")
                            new_data["Plot Width"] = float(land_dimensions[0])
                            new_data["Plot Depth"] = float(land_dimensions[1])

                        except:
                            pass

                        # for sales_history in  property_app['salesHistory'][0:2]:
                        #     sales={}
                        #     sales['sales data']=sales_history['Sale Date']
                        #     sales['sales price']=sales_history['Price']
                        #     property['Appraiser sales history'].append(sales)
                        property["Appraiser owner Name"] = property_appraisal["owner"]
                        property["Appraiser Land Dimensions"] = property_appraisal[
                            "landInformation"
                        ][0]["Land Dimensions"]
                        # property['Appraiser Exterior Wall']=property_app['lblBuildingExteriorWall1']
                        try:
                            for element in property_appraisal["buildingStructuralElements"]:
                                try:
                                    if element["Structural Elements"] == "Exterior Walls:":
                                        new_data["Construction"] = element["Unnamed: 1"]
                                except:
                                    new_data["Construction"] = ""
                        except:
                            new_data["Construction"] = ""

                        try:
                            new_data["Building Sketch"] = property_appraisal["sketch"]
                        except:
                            new_data["Building Sketch"] = ""
                        try:
                            new_data["Appraiser"] = property_appraisal["url"]
                        except:
                            new_data["Appraiser"] = ""

                        try:
                            new_data["Sinkhole"] = property_appraisal["Subsidence"]
                        except:
                            pass
                        try:
                            if new_data["Years owned"] > 1:
                                new_data["Recommendation 2"] = True
                            else:
                                new_data["Recommendation 2"] = False
                        except:
                            pass
                        
    
        


                today_data.append(new_data)

            except Exception as e:
                print(traceback.format_exc())
                print(f"Error processing property {count}: {str(e)}")
                count += 1
                continue

    except Exception as e:
        print(f"Error in find_today_data: {str(e)}")
        traceback.print_exc()

    print(f"Returning {len(today_data)} valid records")
    return today_data




def send_email(sender_email, sender_password, recipients, subject, workbook_date, download_link,
               company_name="Your Company", logo_url=None, cta_text="📥 Download Workbook"):
    try:
        msg = MIMEMultipart("alternative")
        msg["From"] = sender_email
        msg["To"] = ", ".join(recipients)
        msg["Subject"] = subject

        # Use PNG version of the logo for email compatibility
        if not logo_url:
            logo_url = "https://habu-documents.s3.us-east-1.amazonaws.com/logo.png"


        html_body = f"""
<html>
<body style="margin:0; padding:0; font-family: Arial, sans-serif; background: transparent;">
    <table align="center" cellpadding="0" cellspacing="0" width="600" style="border-collapse: collapse; margin: 50px auto; border-radius: 14px; overflow: hidden;  box-shadow: 0 8px 24px rgba(0,0,0,0.05);">
        
        <!-- Header -->
        <tr>
            <td style="padding: 40px 20px; text-align: center;  color: #ffffff;">
    
                <h3 style="margin: 0; color:#000000; font-size: 28px; font-weight: bold;">🔔 {company_name} Update</h3>
            </td>
        </tr>

        <!-- Body -->
        <tr>
            <td style="padding: 50px 35px 40px 35px;">
                <p style="font-size: 18px; margin: 0 0 25px;">👋 Greetings,</p>

                <p style="font-size: 16px; line-height: 1.8; margin: 0 0 30px;">
                    We're pleased to inform you that your <strong>{company_name}</strong> workbook for <strong>Fasst</strong>,
                    dated on <strong>{workbook_date.strftime('%B %d, %Y')}</strong>, is now ready for download. 📊
                </p>

                <!-- CTA -->
                <div style="text-align: center; margin: 50px 0 40px 0;">
                    <a href="{download_link}" style="
                        display: inline-block;
                        color: #fff;
                        background-color: #28a745;
                        text-decoration: none;
                        padding: 14px 26px;
                        font-size: 16px;
                        border-radius: 8px;
                        font-weight: 400;
                        box-shadow: 0 4px 10px rgba(0,0,0,0.08);
                    ">{cta_text}</a>
                </div>

                <!-- Fallback URL -->
                <p style="font-size: 14px; color: #555555; text-align: center; margin-bottom: 10px;">
                    If the button above does not work, please copy and paste the URL below into your browser:
                </p>
                <p style="font-size: 14px; color: #007bff; word-break: break-word; text-align: center; margin-bottom: 50px;">
                    <a href="{download_link}" style="color: #007bff; text-decoration: underline;">{download_link}</a>
                </p>

                <!-- Footer message -->
                <p style="font-size: 16px; line-height: 1.7; margin-bottom: 10px;">💡 If you have any questions or need further assistance, just reply to this email — we’re here to help!</p>
                <p style="font-size: 16px;">Warm regards,<br><strong>{company_name} Team 🤝</strong></p>
            </td>
        </tr>

        <!-- Footer -->
        <tr>
            <td style="padding: 25px; text-align: center; font-size: 13px; color: #999999; border-top: 1px solid #eaeaea;">
                &copy; {company_name} | Powered by 🚀 Habu
            </td>
        </tr>
    </table>
</body>
</html>
"""





        plain_text = f"""Hello,

Your {company_name} workbook for Roofing and Rehab on {workbook_date.strftime('%B %d, %Y')} is now available.

Download it here: {download_link}

Best regards,
{company_name} Team
"""

        msg.attach(MIMEText(plain_text, "plain"))
        msg.attach(MIMEText(html_body, "html"))

        with smtplib.SMTP("smtp.gmail.com", 587) as server:
            server.starttls()
            server.login(sender_email, sender_password)
            server.sendmail(sender_email, recipients, msg.as_string())

        print("Email sent successfully")
    except Exception as e:
        print(f"Error sending email: {str(e)}")
        raise



def generate_download_link(s3_object_key, expiration):
    try:
        s3 = boto3.client("s3")
        url = s3.generate_presigned_url(
            "get_object",
            Params={"Bucket": AWS_BUCKET_NAME, "Key": s3_object_key},
            ExpiresIn=expiration,
        )
        return url
    except Exception as e:
        print(f"Error generating download link: {str(e)}")
        raise


if __name__ == "__main__":
    # Test locally
    handler({"day": "currentDay"}, None)