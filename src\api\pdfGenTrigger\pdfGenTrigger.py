import json
import os
import sys
import boto3
from typing import Dict, Any

# Add the src directory to the Python path
sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)

from helper.db import connect_db

lambda_client = boto3.client('lambda')

def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Source Lambda function that triggers another Lambda in a different service
    """
    try:
        # Get the target function name from environment variables
        target_function=''
        body = json.loads(event['body'])
        address = body.get('address')
        type = body.get('type')
        if not address :
            return {
                "statusCode": 400,
                "body": json.dumps({"error": "Missing 'address' in request."}),
                  "headers": {
                "Access-Control-Allow-Headers": "Content-Type",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS,POST",
            },
            }
        if not type :
            return {
                "statusCode": 400,
                "body": json.dumps({"error": "Missing 'type' in request."}),
                  "headers": {
                "Access-Control-Allow-Headers": "Content-Type",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS,POST",
            },
            }
        # Initialize or update the status in your MongoDB document
        db = connect_db()
        collection = db["PropFromBridge"]

        # Prepare the payload to send to target Lambda
        payload = {
            "message": f"Request for ${type} Lambda",
            "data": body  # Forward the original event or modify as needed
        }
        # Invoke the target Lambda function
        print(os.environ['TARGET_FUNCTION_NAME'])
        collection.update_one(
            {"address": address},
            {"$set": {
                "status.SIRSPdfGenAi.status": 0,
                "status.milestonePdfGenAi.status": 0
            }}
        )

        if type == "sirs":
            target_function=f'arn:aws:lambda:us-east-1:953683027250:function:sirsPdfGenAi-{os.environ["STAGE"]}'
            
        if type == "milestone":
            target_function=f'arn:aws:lambda:us-east-1:953683027250:function:milestonePdfGenAi-{os.environ["STAGE"]}'
        lambda_client.invoke(
            FunctionName=target_function,
            InvocationType='Event',  # Use 'Event' for async invocation
            Payload=json.dumps(payload)
        )
        

        
        
        return {
            "statusCode": 200,
            "body": json.dumps({
                'message': 'Successfully triggered target Lambda',
                'target_response': 'success'
            }),
            "headers": {
                "Access-Control-Allow-Headers": "Content-Type",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS,POST",
            },
        }
        
    except Exception as e:
        print(f"Error invoking target Lambda: {str(e)}")
        return {
            'statusCode': 500,
              "headers": {
                "Access-Control-Allow-Headers": "Content-Type",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS,POST",
            },
            'body': {
                'error': f"Failed to invoke target Lambda: {str(e)}"
            }
        }