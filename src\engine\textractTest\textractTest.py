import json
import boto3
import base64
import os

def lambda_handler(event, context):
    """
    AWS Lambda function that extracts text from PDF files using AWS Textract in synchronous mode.
    
    The function expects the S3 key in event["data"] and uses a configured S3 bucket.
    
    Returns:
        dict: JSON response containing the extracted text or error message
    """
    try:
        # Initialize Textract client
        textract = boto3.client('textract')
        
        # Get S3 key from the event
        if 'data' in event:
            # Get the S3 key from the event data
            s3_key = event['data']
            
            # Get the S3 bucket name from environment variable
            bucket = "generate-report-fasst-python"
            
            # Process the PDF from S3
            extracted_text = process_s3_object(textract, bucket, s3_key)
            
            # Return the extracted text
            return {
                'statusCode': 200,
                'body': json.dumps({
                    'message': 'Text extraction completed successfully',
                    'extractedText': extracted_text,
                    'processedKey': s3_key
                })
            }
        else:
            return {
                'statusCode': 400,
                'body': json.dumps({'error': 'No data field provided in the event'})
            }
            
    except Exception as e:
        print(f"Error: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'error': str(e)
            })
        }

def process_s3_object(textract, bucket, key):
    """
    Process a PDF document stored in an S3 bucket.
    
    Args:
        textract: Textract client
        bucket (str): S3 bucket name
        key (str): S3 object key
        
    Returns:
        str: Extracted text
    """
    print(f"Processing document from S3: {bucket}/{key}")
    
    # Call Textract to detect document text
    response = textract.detect_document_text(
        Document={
            'S3Object': {
                'Bucket': bucket,
                'Name': key
            }
        }
    )
    
    return extract_text_from_response(response)

def extract_text_from_response(response):
    """
    Extract text blocks from Textract response.
    
    Args:
        response (dict): Textract API response
        
    Returns:
        str: Concatenated text from all detected text blocks
    """
    text_blocks = []
    
    # Extract text from each block in the response
    for block in response['Blocks']:
        if block['BlockType'] == 'LINE':
            text_blocks.append(block['Text'])
    
    # Join all text blocks with newlines
    return '\n'.join(text_blocks)