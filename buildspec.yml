version: 0.2

phases:
  install:
    runtime-versions:
      python: 3.9
      nodejs: 18
    commands:
      - echo Installing Serverless Framework...
      - npm install -g serverless@3.38.0
      - npm install -g serverless-plugin-scripts
      - npm install -g serverless-lift

  build:
    commands:
      # - echo Build started on `date`
      # - pip install -r requirements.txt -t ./layers/MyLayer/python
      # - pip install -r requirements2.txt -t ./layers/Layer2/python

  post_build:
    commands:
        - serverless deploy --stage prod
        - serverless doctor

      # - echo Build completed on `date`
artifacts:
  files:
    - '**/*'
  name:  fasst-serverless-cicd
  discard-paths: yes
cache:
  paths:
    - node_modules
    - layers/
  key_files:
    - '**/package-lock.json'