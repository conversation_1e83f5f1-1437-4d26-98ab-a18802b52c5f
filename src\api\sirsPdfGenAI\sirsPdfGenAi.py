import os
import json
import time
import logging

import concurrent.futures
import boto3
from pymongo import MongoClient
from bson.objectid import ObjectId
from trp import Document
from typing import List, Dict, Any, Optional, Tuple
from pydantic import BaseModel, Field

from langchain.prompts import SystemMessagePromptTemplate, HumanMessagePromptTemplate, ChatPromptTemplate
from langchain_openai import Chat<PERSON>penAI
from langchain_google_genai import Chat<PERSON><PERSON>gleGenerativeA<PERSON>
from langchain.output_parsers import PydanticOutputParser
from llama_parse import LlamaParse

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Environment variables
# MONGO_URI = os.environ.get("PYTHON_DB_URL")  # e.g. mongodb://user:pw@host:27017/admin
# S3_BUCKET = os.environ.get("S3_BUCKET_NAME")  # e.g. generate-report-fasst-python
MONGO_URI = "mongodb://mongdbuser:%266XR%24a%25jS2W5z%5E%40%24@***********:27017/admin"
S3_BUCKET = "fasstcondo-public-files"

STAGE = os.environ.get("STAGE", "dev")

# AWS clients
textract = boto3.client('textract')
s3 = boto3.client('s3')
lambda_client = boto3.client('lambda')

# Mongo connection helper
def get_mongo_client():
    return MongoClient(MONGO_URI)

# Pydantic models for data structures
class TableRow(BaseModel):
    """A single row in a table with dynamic fields."""
    row_data: Dict[str, Any] = Field(description="The data for this row with column names as keys")
    parent_category: str = Field(default="", description="Parent category for hierarchical tables")
    is_subcategory: bool = Field(default=False, description="Whether this row represents a subcategory")

class TableData(BaseModel):
    """A structured representation of a table."""
    table_content: str = Field(description="Raw table content in markdown format")
    headers: List[str] = Field(description="The column headers of the table")
    rows: List[TableRow] = Field(description="The rows of data in the table")
    page_number: int = Field(description="Page number where the table was found")
    table_number: int = Field(description="Table number on the page")

class PDFTableExtraction(BaseModel):
    """The complete extraction result for a PDF."""
    pdf_name: str = Field(description="Name of the PDF file")
    tables: List[TableData] = Field(description="List of tables extracted from the PDF")

class IncreaseBetweenFunding(BaseModel):
    """Structure for increase between current and future funding."""
    percentageIncrease: str = Field(default="", description="Percentage increase between current and future funding")
    dollarIncreasePerUnit: str = Field(default="", description="Dollar increase per unit between current and future funding")
    dollarIncreaseTotal: str = Field(default="", description="Total dollar increase between current and future funding")

class CurrentReserveFunding(BaseModel):
    """Structure for current reserve funding."""
    description: str = Field(default="Current Reserve Funding is the starting balance of the reserve fund of the current year.")
    currentReserve: str = Field(default="", description="Current reserve fund balance")

class RequiredReserveFunding(BaseModel):
    """Structure for required reserve funding."""
    description: str = Field(default="It is either Required Reserve Funding, or Full Funded Balance, or Total Recommended Reserve Contributions. And if there is reserve fund for Structural and General type then the required reserve is the sum of both.")
    structuralReserve: str = Field(default="", description="Structural reserve fund balance")
    generalReserve: str = Field(default="", description="General reserve fund balance")
    requiredReserve: str = Field(default="", description="Required reserve fund balance (sum of structural and general)")

class ReserveFunds(BaseModel):
    """Structure for reserve funds."""
    currentReserveFunding: CurrentReserveFunding = Field(default_factory=CurrentReserveFunding)
    requiredReserveFunding: RequiredReserveFunding = Field(default_factory=RequiredReserveFunding)

class SIRSData(BaseModel):
    """Structure for SIRS data extraction."""
    totalUnits: str = Field(default="", description="Total number of units")
    averageMonthlyContributionPerUnit: str = Field(default="", description="Average monthly contribution per unit")
    additionalContributionForAllUnits: str = Field(default="", description="Additional contribution for all units")
    specialAssessments: str = Field(default="", description="Special assessments")
    increaseBetweenCurrentAndFutureFunding: IncreaseBetweenFunding = Field(default_factory=IncreaseBetweenFunding)
    reserveFunds: ReserveFunds = Field(default_factory=ReserveFunds)
    page_references: Dict[str, List[int]] = Field(
        default_factory=lambda: {
            "totalUnits": [],
            "averageMonthlyContributionPerUnit": [],
            "additionalContributionForAllUnits": [],
            "specialAssessments": [],
            "increaseBetweenCurrentAndFutureFunding": [],
            "reserveFunds": []
        },
        description="Page references for each extracted data point"
    )
    confidence: int = Field(default=0, description="Confidence score for the extraction results (0-100)")
    summary: str = Field(default="", description="Summary of the extracted data")
    observations: List[str] = Field(default_factory=list, description="Observations made during extraction")

# -----------------------------
# Helper functions from both scripts
# -----------------------------

# Create a class to maintain compatibility with code expecting a 'Document' object
class ProcessedExtraction:
    def __init__(self, tables):
        self.tables = tables

def get_llm(provider="openai"):
    """Get the appropriate LLM based on the provider."""
    if provider.lower() == "openai":
        # openai_api_key = os.getenv("OPENAI_API_KEY")
        openai_api_key = "********************************************************************************************************************************************************************"
        if not openai_api_key:
            logger.error("OpenAI API key not found. Please set OPENAI_API_KEY in your .env file.")
            return None
        return ChatOpenAI(model="gpt-4o-mini", temperature=0, api_key=openai_api_key)
    elif provider.lower() == "google":
        # google_api_key = os.getenv("GOOGLE_API_KEY")
        google_api_key = "AIzaSyAn2l1eGXPMUKw5VObUZp17jonRLDrjt9g"
        if not google_api_key:
            logger.error("Google API key not found. Please set GOOGLE_API_KEY in your .env file.")
            return None
        return ChatGoogleGenerativeAI(model="gemini-1.5-pro", temperature=0, google_api_key=google_api_key)
    else:
        logger.error(f"Unsupported LLM provider: {provider}")
        return None

def extract_tables_with_textract(s3_uri: str, required_keywords=None):
    """
    Extract tables using AWS Textract & TRP Document.
    s3_uri: 's3://bucket/key.pdf'
    """
    bucket, key = s3_uri.replace("s3://", "").split('/', 1)
    response = textract.start_document_analysis(
        DocumentLocation={"S3Object": {"Bucket": bucket, "Name": key}},
        FeatureTypes=["TABLES"]
    )
    job_id = response['JobId']
    logging.info(f"Started Textract table job: {job_id}")

    while True:
        status = textract.get_document_analysis(JobId=job_id)['JobStatus']
        if status == 'SUCCEEDED':
            break
        if status == 'FAILED':
            raise RuntimeError('Textract job failed')
        time.sleep(2)

    pages = []
    next_token = None
    while True:
        kwargs = {'JobId': job_id}
        if next_token:
            kwargs['NextToken'] = next_token
        resp = textract.get_document_analysis(**kwargs)
        pages.append(resp)
        next_token = resp.get('NextToken')
        if not next_token:
            break

    all_blocks = []
    for p in pages:
        all_blocks.extend(p.get('Blocks', []))
    merged = {'DocumentMetadata': pages[0].get('DocumentMetadata', {}), 'Blocks': all_blocks}

    doc = Document(merged)
    tables_by_page = {}
    for i, page in enumerate(doc.pages):
        content_list = []
        for tbl in page.tables:
            header = [c.text.strip() for c in tbl.rows[0].cells]
            sep = ['---'] * len(header)
            rows = [[c.text.strip() for c in row.cells] for row in tbl.rows[1:]]
            md = [f"| {' | '.join(header)} |", f"| {' | '.join(sep)} |"]
            for r in rows:
                md.append(f"| {' | '.join(r)} |")
            md_str = "\n".join(md)
            if not required_keywords or all(kw.lower() in md_str.lower() for kw in required_keywords):
                content_list.append(md_str)
        if content_list:
            tables_by_page[i] = content_list
    return tables_by_page

def parse_pdf_for_sirs_data(s3_uri: str):
    """
    Use LlamaParse to extract full text chunks from PDF at S3 URI.
    """
    bucket, key = s3_uri.replace("s3://", "").split('/', 1)
    tmp = '/tmp/' + key.split('/')[-1]
    s3.download_file(bucket, key, tmp)

    # api_key = os.getenv("LLAMA_CLOUD_API_KEY")
    api_key = "llx-FUYtzZiM3SDG9H6nxAv9xjeVhBdQTtwBrQ3cICtBbvJLF6fa"
    parser = LlamaParse(
        api_key=api_key,
        result_type="markdown",
        mode_parameters={'add_nougat_ocr': True}
    )
    return parser.load_data(tmp)

def process_single_table(args: Tuple):
    """Process a single table with LLM - designed for parallel processing."""
    table_content, page_num, table_idx, _, llm, parser, chat_prompt = args
    try:
        formatted_prompt = chat_prompt.format_prompt(
            table_content=table_content,
            page_number=page_num + 1,
            table_number=table_idx + 1,
            format_instructions=parser.get_format_instructions()
        )

        response = llm.invoke(formatted_prompt.to_messages())
        parsed_table = parser.parse(response.content)
        table_data = TableData(
            table_content=table_content,
            headers=parsed_table.headers,
            rows=parsed_table.rows,
            page_number=page_num + 1,
            table_number=table_idx + 1
        )
        return table_data
    except Exception as e:
        logger.error(f"Error processing table {table_idx+1} on page {page_num+1}: {str(e)}")
        return None
    
def process_tables_with_llm(tables_by_page, pdf_name, llm_provider="openai"):
    """Process tables using the specified LLM provider to structure the data with enhanced handling for inconsistent data."""
    try:
        llm = get_llm(llm_provider)
        if not llm:
            return PDFTableExtraction(pdf_name=pdf_name, tables=[])

        logger.info(f"Using {llm_provider} for table processing")
        pdf_extraction = PDFTableExtraction(pdf_name=pdf_name, tables=[])
        parser = PydanticOutputParser(pydantic_object=TableData)

        system_template = """You are an expert at analyzing and structuring table data from PDFs, specifically for reserve studies of building components.
            Your task is to identify and extract tables from the provided content, then convert them into a structured format.
            Extract the headers and all rows of data accurately.

            You should be able to identify tables even if they don't use the traditional markdown format with | characters.
            Look for aligned columns of data, especially financial data with dollar amounts, percentages, or numerical values.

            IMPORTANT - SIRS reports often have different formats:
            1. Some tables have categories as separate rows with components listed below them
            2. Other tables have the category in the same row as the component
            3. Some tables have a dedicated "Category" column
            4. Tables without BOTH useful life AND remaining life data should be ignored
            
            Pay special attention to:
            1. Reserve Component/Item names - These are the specific building components being analyzed
            2. Life values - We need BOTH useful/expected life AND remaining life (may be formatted as years, ranges, or "XX:XX")
            3. Category headings - They often appear as separate rows or section headers
            
            For life values:
            - When multiple "Life" columns appear, identify which is "Useful Life" and which is "Remaining Life"
            - If only a single generic "Life" column appears with no way to determine if it's useful or remaining, this data is not useful
            - We need BOTH useful life AND remaining life data to properly analyze components
            
            Your primary goal is to extract both the component information AND its relationship to useful and remaining life."""

        human_template = """Here is content extracted from a PDF:

            {table_content}

            First, identify if there are any tables in this content that contain BOTH useful life AND remaining life information. 
            If there are multiple tables, focus on the most prominent one that has both types of life data.
            Then extract the headers and rows from the identified table. The content is from page {page_number}, table number {table_number}.

            Important instructions:
            1. ONLY process tables that have BOTH useful/expected life AND remaining life data
               - A table with only a single "Life" column and no way to tell if it's useful or remaining should be ignored
               - If you can't identify both useful and remaining life data, respond with empty headers and rows
            
            2. Pay special attention to category headings or sections:
               - Sometimes they appear as bold/highlighted rows without data (e.g., "Exterior Building Elements")
               - Other times they appear in a dedicated column
               - Always capture this category-component relationship
            
            3. If the category is a separate row, set it as the parent_category for all rows that follow until a new category is found
            
            4. For rows that have a reserve component name, make sure to identify:
               - The component name (e.g., "Balconies, Concrete, Repairs")
               - BOTH useful life and remaining life values
               - Its parent category (from context if not in the same row)
               
            5. For tables without explicit life values but with replacement years, try to infer useful and remaining life:
               - Calculate remaining life from replacement year minus current year
               - Look for patterns to infer useful life values
            
            6. If you cannot identify BOTH useful and remaining life data for this table, return empty headers and rows
            
            {format_instructions}"""

        chat_prompt = ChatPromptTemplate.from_messages([
            SystemMessagePromptTemplate.from_template(system_template),
            HumanMessagePromptTemplate.from_template(human_template)
        ])

        all_tables = []
        for page_num, tables in tables_by_page.items():
            for table_idx, table_content in enumerate(tables):
                all_tables.append((table_content, page_num, table_idx, pdf_name, llm, parser, chat_prompt))

        total_tables = len(all_tables)
        logger.info(f"Processing {total_tables} tables in parallel")

        max_workers = min(total_tables, 10)
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            if logger.level <= logging.DEBUG:
                try:
                    from tqdm import tqdm
                    results = list(tqdm(executor.map(process_single_table, all_tables), total=total_tables, desc="Processing tables"))
                except ImportError:
                    results = list(executor.map(process_single_table, all_tables))
            else:
                results = list(executor.map(process_single_table, all_tables))

        successful_tables = 0
        for table_data in results:
            if table_data:
                # Post-process to handle common issues
                if not table_data.headers:
                    continue  # Skip tables with no headers after processing
                if not table_data.rows:
                    continue  # Skip tables with no rows after processing
                
                # Make sure all rows have complete data
                for row in table_data.rows:
                    if not row.row_data:
                        row.row_data = {}
                    
                    # Ensure all headers are represented in row_data
                    for header in table_data.headers:
                        if header not in row.row_data:
                            row.row_data[header] = ""
                    
                    # Check for misplaced monetary values in category fields
                    category_field = None
                    for field in ["Category", "category", "CATEGORY"]:
                        if field in row.row_data:
                            category_field = field
                            break
                    
                    if category_field and row.row_data[category_field]:
                        value = row.row_data[category_field]
                        # If category contains $ sign, it's likely a monetary value misplaced
                        if isinstance(value, str) and "$" in value:
                            # Try to use parent_category instead if available
                            if row.parent_category:
                                row.row_data[category_field] = row.parent_category
                            else:
                                # If no parent_category, try to use a reasonable default
                                row.row_data[category_field] = "Building Component"
                    
                    # Check for missing reserve item/component name
                    reserve_item_field = None
                    for field in ["Reserve Item", "reserve item", "Component", "component", "Description", "description"]:
                        if field in row.row_data:
                            reserve_item_field = field
                            break
                    
                    # If no explicit reserve item field found, check if there's an unlabeled first column
                    if not reserve_item_field and len(table_data.headers) > 0:
                        first_header = table_data.headers[0]
                        if first_header not in ["Category", "category", "CATEGORY"]:
                            reserve_item_field = first_header
                    
                    # If reserve item field contains $ value, it's likely a misalignment
                    if reserve_item_field and "$" in str(row.row_data.get(reserve_item_field, "")):
                        # Try to find a better field for the reserve item
                        for header, value in row.row_data.items():
                            if header != reserve_item_field and isinstance(value, str) and not "$" in value and value.strip():
                                row.row_data[reserve_item_field] = value
                                break
                
                # Verify table has both useful life and remaining life data after processing
                headers_lower = [h.lower() if isinstance(h, str) else "" for h in table_data.headers]
                
                # Check for useful life and remaining life in headers
                has_useful_life = any(term in " ".join(headers_lower) for term in 
                                     ["useful life", "expected life", "service life"])
                has_remaining_life = any(term in " ".join(headers_lower) for term in 
                                        ["remaining life", "rem. life", "rem life"])
                
                # If we have both explicit columns, add the table
                if has_useful_life and has_remaining_life:
                    pdf_extraction.tables.append(table_data)
                    successful_tables += 1
                    continue
                
                # If we don't have explicit headers, check if we can identify life columns by pattern
                life_headers = [h for h in headers_lower if "life" in h]
                if len(life_headers) >= 2:
                    # If we have at least two life-related columns, assume one is useful and one is remaining
                    pdf_extraction.tables.append(table_data)
                    successful_tables += 1
                    continue
                
                # Last check: look for life data in the row content
                has_life_data_in_rows = False
                for row in table_data.rows:
                    row_data_str = str(row.row_data).lower()
                    useful_life_in_row = any(term in row_data_str for term in 
                                           ["useful life", "expected life", "service life"])
                    remaining_life_in_row = any(term in row_data_str for term in 
                                              ["remaining life", "rem. life", "rem life"])
                    if useful_life_in_row and remaining_life_in_row:
                        has_life_data_in_rows = True
                        break
                
                if has_life_data_in_rows:
                    pdf_extraction.tables.append(table_data)
                    successful_tables += 1
                else:
                    # Single "Life" column table - check if this is the case we want to avoid
                    if len(life_headers) == 1 and life_headers[0] == "life":
                        logger.info(f"Skipping table from page {table_data.page_number}, table {table_data.table_number} - only has a single generic 'Life' column")
                    else:
                        logger.info(f"Skipping table from page {table_data.page_number}, table {table_data.table_number} - insufficient life data")

        logger.info(f"Successfully processed {successful_tables} of {total_tables} tables with both useful and remaining life data")
        return pdf_extraction.tables  # Return just the tables list for compatibility with your existing code
    except Exception as e:
        logger.error(f"Error processing tables with LLM: {str(e)}")
        return []

def extract_sirs_data(s3_uri: str, extraction_results, provider="openai"):
    """
    Extract SIRS data from the PDF using a dedicated document parsing and LLM analysis.
    Uses LlamaParse for complete text extraction and structured analysis.
    """
    try:
        llm = get_llm(provider)
        if not llm:
            return SIRSData()

        # Parse the complete PDF document for SIRS data extraction
        docs = parse_pdf_for_sirs_data(s3_uri)
        if not docs:
            logger.error("Failed to parse PDF for SIRS data extraction")
            return SIRSData(observations=["Failed to parse PDF for complete text analysis"])

        # Combine document text for comprehensive analysis
        full_text = ''
        for doc in docs:
            full_text += f"\n--- PAGE {doc.metadata.get('page_number', 0)} ---\n" + doc.text

        # Format table summaries from the extraction results
        table_summaries = []
        for table in extraction_results:
            table_summary = f"\n--- TABLE FROM PAGE {table.page_number} ---\n"
            table_summary += f"Headers: {', '.join(table.headers)}\n"
            table_summary += "Rows:\n"
            for row in table.rows:
                row_summary = f"  {json.dumps(row.row_data)}"
                if row.parent_category:
                    row_summary += f" (Parent: {row.parent_category})"
                if row.is_subcategory:
                    row_summary += " (Subcategory)"
                table_summary += row_summary + "\n"
            table_summaries.append(table_summary)
        
        table_text = "\n".join(table_summaries)

        # Create extraction prompt
        extraction_prompt = """
You are a seasoned expert in Structural Integrity Reserve Studies (SIRS) with extensive knowledge in structural engineering, document analysis, and technical evaluations. Your goal is to extract and interpret key data from a provided PDF SIRS report. The report may use varied layouts, labeling, or terminology. As such, be flexible and exhaustive in your search for data—if a term isn't exactly as expected, consider synonymous language or context clues that indicate the same meaning.

### 1. Financial Data Extraction

**Data Points to Extract:**

- **Total Number of Units:**  
  - Look for phrases such as "Number of Units," "Total Units," or similar indicators.  
  - *Example:* If you see "Total Units: 150," record 150.

- **Average Monthly Contribution per Unit:**  
  - Search for a directly stated value.  
  - If not found, check if there is an "Annual Contribution" or "Total Annual Contribution."  
  - In that case, compute the average monthly contribution by dividing the annual contribution by the total number of units and then by 12 (months).  
  - If any necessary value (annual contribution or total units) is missing, output "NA."  
  - *Example:* An annual contribution of $36,000 divided among 150 units gives $240 per unit annually, which further divides to $20 per month.

- **Additional Annual Contribution for All Units Combined:**  
  - Look for terms like "Annual Contribution Requirement," "Recommended Annual Funding," "Annual Funding Requirement," or similar.  
  - *Example:* If the report states "Additional annual contribution required is $50,000," record that value.

- **Special Assessments Applicability:**  
  - Determine if any special assessments are mentioned, and output "Yes" if applicable, or "No" if they are not noted.  
  - Be alert for language such as "special assessment," "one-time fee," or related terminology.

- **Increase Between Current and Future Funding Needs:**  
  - Identify the current and future reserve funding or requirements.  
  - If both are present, calculate the increase in:
    - Percentage terms: ((Future – Current) / Current * 100)
    - Dollar amounts per unit: (Total Increase / Total Units)
    - Total dollar increase: (Future – Current)
  - If the values are not directly provided, compare available figures such as initial reserve balances and projected future reserve requirements.  
  - If you cannot derive this information, leave the field empty and note that a calculation was attempted but data was missing.

### 2. Reserve Funds Extraction

For the reserve funding section, follow these steps in order:

- **Step 1:**  
  - Look directly for a "Fully Funded (ideal) reserve balance" value.  
  - If found, assign this value to the `requiredReserve` field and then proceed directly to the calculation of `structuralReserve` and `generalReserve` if necessary.

- **Step 2:**  
  - If Step 1 does not yield a value, search for terms such as "Required Reserve," "Total Required Reserve," "Fully Funded Balance (FFB)," "Recommended Reserve Funding," or "Total Recommended Reserve Funding."  
  - If one of these is found, assign it to `requiredReserve` and then skip directly to calculating `structuralReserve` and `generalReserve` if additional breakdowns are provided.

- **Step 3:**  
  - If neither a direct "Fully Funded (ideal) reserve balance" nor a "Required Reserve" value is found, then:
    - First, look for the general reserve fund (or similar phrasing) and assign it to `generalReserve`.  
    - Second, look for the structural integrity reserve fund (or similar phrasing) and assign it to `structuralReserve`.  
    - If specific categories are not found, search for any summary table that lists total reserve expenditures and attempt to discern the appropriate values for `structuralReserve` and `generalReserve` based on context and labels.

- **Step 4:**  
  - Calculate `requiredReserve` by summing `structuralReserve` and `generalReserve` if both are available.

I'll provide you the document text, followed by extracted tables. Analyze both to extract the needed information.

Additionally, for each data point you extract, note the page number(s) where you found the information. Also provide a confidence score (0-100) for your overall extraction, with 100 being completely confident and 0 being not confident at all. Finally, provide a concise summary of your findings (100-200 words).

I NEED THE FINAL OUTPUT TO BE FORMATTED AS A VALID JSON OBJECT with the following structure:
{
  "totalUnits": "",
  "averageMonthlyContributionPerUnit": "",
  "additionalContributionForAllUnits": "",
  "specialAssessments": "",
  "increaseBetweenCurrentAndFutureFunding": {
    "percentageIncrease": "",
    "dollarIncreasePerUnit": "",
    "dollarIncreaseTotal": ""
  },
  "reserveFunds": {
    "currentReserveFunding": {
      "description": "Current Reserve Funding is the starting balance of the reserve fund of the current year.",
      "currentReserve": ""
    },
    "requiredReserveFunding": {
      "description": "It is either Required Reserve Funding, or Full Funded Balance, or Total Recommended Reserve Contributions. And if there is reserve fund for Structural and General type then the required reserve is the sum of both.",
      "structuralReserve": "",
      "generalReserve": "",
      "requiredReserve": ""
    }
  },
  "page_references": {
    "totalUnits": [],
    "averageMonthlyContributionPerUnit": [],
    "additionalContributionForAllUnits": [],
    "specialAssessments": [],
    "increaseBetweenCurrentAndFutureFunding": [],
    "reserveFunds": []
  },
  "confidence": 0,
  "summary": "",
  "observations": []
}

Format all dollar values consistently (e.g., "$1,200,000" not "1.2M" or "1,200,000"). Include the dollar sign.
Format all percentages with the % symbol (e.g., "25%" not "0.25" or "25").
For the "page_references" field, include the page numbers where you found each piece of information as arrays of integers.
For the "confidence" field, provide a value from 0-100 representing your overall confidence in the extraction.
For the "summary" field, provide a concise 100-200 word summary of your findings.
For the "observations" field, include a list of notes about what you found, what was missing, where you looked, and any assumptions made.

Additionally, at the end of your JSON output, include an "Observations" section where you detail:
- **What You Could Not Find:**  
  - List any data points that were missing and explain why these values were not found (e.g., they were not present, the report used alternate language, or the tables did not include the expected headings).
- **Where You Looked:**  
  - Describe the sections of the report that were analyzed (e.g., financial summaries, reserve funding tables, building components tables, narrative sections).
- **Assumptions Made:**  
  - Explain any assumptions you made during the extraction process (e.g., averaging range values, interpreting synonymous terminology).
- **Suggestions for Locating Missing Data:**  
  - Recommend what sections or alternative labels to check in other types of reports, should similar data be presented differently.
"""

        # Create final prompt combining document and tables
        final_prompt = f"{extraction_prompt}\n\n### DOCUMENT TEXT:\n{full_text[:100000]}\n\n### EXTRACTED TABLES:\n{table_text[:50000]}"

        # Get response from LLM
        logger.info("Sending SIRS data extraction request to LLM")
        
        # Create chat messages
        messages = [
            {"role": "system", "content": "You are a document analysis expert specializing in extracting structured data from Structural Integrity Reserve Studies."},
            {"role": "user", "content": final_prompt}
        ]
        
        response = llm.invoke(messages)
        
        # Parse JSON from response
        logger.info("Parsing SIRS data from LLM response")
        response_text = response.content
        
        # Extract JSON from response (handling cases where there might be additional text)
        json_start = response_text.find('{')
        json_end = response_text.rfind('}') + 1
        if json_start >= 0 and json_end > json_start:
            json_str = response_text[json_start:json_end]
            try:
                extracted_data = json.loads(json_str)
                
                # Validate and fix page_references structure if needed
                if "page_references" in extracted_data:
                    for key in ["totalUnits", "averageMonthlyContributionPerUnit", 
                                "additionalContributionForAllUnits", "specialAssessments", 
                                "increaseBetweenCurrentAndFutureFunding", "reserveFunds"]:
                        value = extracted_data["page_references"].get(key)
                        if not isinstance(value, list):
                            # Log or print the incorrect structure for debugging
                            print(f"⚠️ Invalid page reference type for '{key}': {type(value)}. Resetting to empty list.")
                            extracted_data["page_references"][key] = []

                                
                # Create the SIRS data object
                sirs_data = SIRSData(**extracted_data)
                logger.info("Successfully extracted SIRS data")
                return sirs_data
            except Exception as e:
                logger.error(f"Error parsing JSON from LLM response: {str(e)}")
                logger.debug(f"JSON string attempted to parse: {json_str}")
                
                # Create basic SIRS data with error observation
                sirs_data = SIRSData(
                    observations=["Error parsing extraction results from LLM"]
                )
                return sirs_data
        else:
            logger.error("No valid JSON found in LLM response")
            
            # Create basic SIRS data with error observation
            sirs_data = SIRSData(
                observations=["No valid extraction results found in LLM response"]
            )
            return sirs_data
            
    except Exception as e:
        logger.error(f"Error extracting SIRS data: {str(e)}")
        return SIRSData(observations=[f"Error during extraction: {str(e)}"])


def extract_component_data(extraction_results, provider="openai"):
    """
    Process extracted tables to create a standardized component data structure.
    
    Args:
        extraction_results: Either a Document object with tables property or a list of TableData objects
        provider: The LLM provider to use ("openai" or "google")
        
    Returns:
        A list of components in a standardized format.
    """
    logger.info("Extracting component data in standardized format")
    llm = get_llm(provider)
    if not llm:
        return []
    
    all_components = []
    
    # Process each table in the extraction results
    for tbl in extraction_results:
        # Convert table to standardized format to send to LLM
        table_data = {
            "headers": tbl.headers,
            "rows": []
        }
        
        for row in tbl.rows:
            table_data["rows"].append({
                "data": row.row_data,
                "parent_category": row.parent_category,
                "is_subcategory": row.is_subcategory
            })
        
        # Create prompts for extracting component data
        system_prompt = """You are an expert at analyzing reserve study data tables and extracting component information.
        Your task is to convert a table of building components into a standardized format with specific fields.
        
        For each component in the table, extract:
        1. parent_category: The main category of the component (e.g., "Building Service Components")
        2. reserve_item: The name of the specific component (e.g., "Roof, TPO")
        3. useful_life: The expected total lifespan (in years)
        4. remaining_life: The time left before replacement (in years)
        
        Rules to follow:
        - If multiple "Life" columns exist, the larger value is usually "useful_life"
        - The smaller value is usually "remaining_life"
        - Format life values consistently without units (e.g., "25" not "25 years" or "25:00")
        - If a parent_category is missing in a row, use the most recent one from previous rows
        - Ensure reserve_item is always a descriptive text, not a code or monetary value
        - Format all extracted data as strings (including numeric values)
        - Do not include rows with "0" or empty useful_life or remaining_life
        - Do not include summary/total rows (e.g., "Elevators Total")
        - Do not include rows where parent_category and reserve_item are identical or too similar
        - Useful life values should be between 1-99 years (not extremely large numbers like 6000+)
        - Remaining life values should be between 1-99 years (not extremely large numbers)
        - Try to standardize similar reserve item names to avoid duplication
        
        The output must strictly follow this format:
        [
            {"parent_category": "...", "reserve_item": "...", "useful_life": "...", "remaining_life": "..."},
            ...
        ]"""
        
        user_prompt = f"""Here is a table from a reserve study. 
        Convert it to the standardized component data format described in your instructions.
        
        Table Headers: {tbl.headers}
        
        Table Rows: 
        {json.dumps(table_data["rows"], indent=2)}
        
        Return ONLY a valid JSON array containing objects with exactly these fields:
        - parent_category
        - reserve_item
        - useful_life
        - remaining_life
        
        IMPORTANT FILTERING REQUIREMENTS:
        1. Do NOT include components with "0" or empty useful_life or remaining_life values
        2. Do NOT include summary rows (those containing words like "Total", "Subtotal", "Sum")
        3. Do NOT include rows where parent_category and reserve_item are the same or very similar
        4. Do NOT include components with unreasonable life values (e.g., 6000+ years)
        5. Typical useful_life values should be between 1-100 years
        6. Make sure values are not dates like 44402 (which is a date code)
        7. Try to standardize similar reserve item names to avoid duplication in the final output
        
        DO NOT include any explanations, just the JSON array."""
        
        # Call LLM to extract component data
        try:
            logger.info(f"Processing table on page {tbl.page_number}")
            response = llm.invoke([
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ])
            response_text = response.content
            
            # Extract JSON array from response
            json_start = response_text.find('[')
            json_end = response_text.rfind(']') + 1
            
            if json_start >= 0 and json_end > json_start:
                components = json.loads(response_text[json_start:json_end])
                
                # Filter out invalid components
                valid_components = []
                for comp in components:
                    # Skip if missing essential data
                    if not (comp.get("parent_category") and comp.get("reserve_item")):
                        continue
                    
                    # Skip if useful_life or remaining_life is "0" or empty
                    useful_life = comp.get("useful_life", "")
                    remaining_life = comp.get("remaining_life", "")
                    
                    if useful_life == "0" or useful_life == "" or remaining_life == "0" or remaining_life == "":
                        continue
                        
                    # Check for unreasonably large life values (likely dates or errors)
                    try:
                        useful_life_num = float(useful_life)
                        remaining_life_num = float(remaining_life)
                        
                        # Skip if values are too large
                        if useful_life_num > 100 or remaining_life_num > 100:
                            continue
                            
                    except (ValueError, TypeError):
                        # If we can't convert to float, keep it
                        pass
                        
                    # Skip if reserve_item contains "total" or similar summary words
                    reserve_item = comp.get("reserve_item", "").lower()
                    if any(word in reserve_item for word in ["total", "subtotal", "sum", "summary"]):
                        continue
                        
                    # Skip if parent_category and reserve_item are identical or too similar
                    parent_category = comp.get("parent_category", "").lower()
                    if parent_category == reserve_item or (
                        parent_category in reserve_item and len(parent_category) > 3) or (
                        reserve_item in parent_category and len(reserve_item) > 3):
                        continue
                    
                    # Add valid component
                    valid_components.append(comp)
                
                all_components.extend(valid_components)
                logger.info(f"Extracted {len(valid_components)} components from table on page {tbl.page_number}")
            
        except Exception as e:
            logger.error(f"Error processing table on page {tbl.page_number}: {str(e)}")
            continue
    
    logger.info(f"Total components extracted: {len(all_components)}")
    return all_components

# def transform_component_data(components_data):
#     """
#     Transform flat component data array into a nested object structure by category.
    
#     Args:
#         components_data: List of components with parent_category, reserve_item, useful_life, and remaining_life
        
#     Returns:
#         A nested object structure grouped by category
#     """
#     transformed_data = {}
    
#     for component in components_data:
#         # Skip if missing critical data
#         if not component.get('parent_category') or not component.get('reserve_item'):
#             continue
            
#         # Create a key from the parent category (lowercase, no spaces, with "Elements" suffix)
#         category_key = component['parent_category'].lower().replace(' ', '') + 'Elements'
        
#         # Create the category if it doesn't exist
#         if category_key not in transformed_data:
#             transformed_data[category_key] = {}
            
#         # Get the reserve item name
#         item_name = component['reserve_item']
        
#         # Skip if the item already exists (avoid duplicates)
#         if item_name in transformed_data[category_key]:
#             continue
            
#         # Add the component with the desired structure
#         transformed_data[category_key][item_name] = {
#             "description": item_name,
#             "useful_life": component.get('useful_life', ''),
#             "estimatedRemainingLifeYears": component.get('remaining_life', '')
#         }
    
#     return transformed_data

def transform_component_data(components_data):
    """
    Transform flat component data array into a nested object structure by category
    without adding unnecessary suffixes.
    
    Args:
        components_data: List of components with parent_category, reserve_item, useful_life, and remaining_life
        
    Returns:
        A nested object structure grouped by category
    """
    transformed_data = {}
    
    for component in components_data:
        # Skip if missing critical data
        if not component.get('parent_category') or not component.get('reserve_item'):
            continue
            
        # Create a key from the parent category (lowercase, no spaces, WITHOUT any suffix)
        category_key = component['parent_category'].lower().replace(' ', '')
        
        # Create the category if it doesn't exist
        if category_key not in transformed_data:
            transformed_data[category_key] = {}
            
        # Get the reserve item name
        item_name = component['reserve_item']
        
        # Skip if the item already exists (avoid duplicates)
        if item_name in transformed_data[category_key]:
            continue
            
        # Add the component with the desired structure
        transformed_data[category_key][item_name] = {
            "description": item_name,
            "useful_life": component.get('useful_life', ''),
            "estimatedRemainingLifeYears": component.get('remaining_life', '')
        }
    
    return transformed_data

# -----------------------------
# Main Lambda handler
# -----------------------------
def lambda_handler(event, context):
    logging.info(f"Event: {event}")
    data = event.get('data', {})
    address = data.get('address')
    if not address:
        return {"statusCode": 400, "body": json.dumps({"error": "Missing address"})}

    client = get_mongo_client()
    db = client['Fasst']
    coll = db['PropFromBridge']
    prop = coll.find_one({'address': address})
    if not prop:
        return {"statusCode": 404, "body": json.dumps({"error": "Address not found"})}

    pid = ObjectId(prop['_id'])
    key = prop['sirsReportKey']
    s3_uri = f"s3://{S3_BUCKET}/{key}"
    # llm_provider = os.getenv("LLM_PROVIDER", "openai")
    llm_provider = "openai"

    try:
        # Step 1: Textract table extraction
        coll.update_one({'_id': pid}, {'$set': {'status.judgementFileTextract.status': 1}})
        
        # When setting status to 1, remove any existing error message
        coll.update_one(
            {'_id': pid}, 
            {
                '$set': {
                    'status.tableExtraction.status': 1,
                    'status.SIRSPdfGenAi.status': 1
                },
                '$unset': {
                    'status.SIRSPdfGenAi.error': ""  # Remove any existing error
                }
            }
        )

        tables_by_page = extract_tables_with_textract(s3_uri)
        coll.update_one({'_id': pid}, {'$set': {
            'status.judgementFileTextract.status': 2
        }})
        
        # Step 2: Process tables with LLM to create structured representation
        structured_tables = process_tables_with_llm(tables_by_page, key, llm_provider)
        print(structured_tables)
        
        # Check if we got valid tables, if not set appropriate error status
        if not structured_tables:
            coll.update_one({'_id': pid}, {'$set': {
                'status.SIRSPdfGenAi.status': 3,  # Status code 3 for no valid tables found
                'status.SIRSPdfGenAi.error': "No valid tables with life data found in document"
            }})
            return {"statusCode": 200, "body": json.dumps({"message": "No valid tables found"})}
        
        # Step 3: Extract component data
        component_data = extract_component_data(structured_tables, provider=llm_provider)

        # Check if we got valid component data
        if not component_data:
            coll.update_one({'_id': pid}, {'$set': {
                'status.SIRSPdfGenAi.status': 4,  # Status code 4 for no valid component data
                'status.SIRSPdfGenAi.error': "Failed to extract component data from tables"
            }})
            return {"statusCode": 200, "body": json.dumps({"message": "No valid component data extracted"})}
        
        # Step 4: Transform component data into the desired structure
        transformed_components = transform_component_data(component_data)

        # Step 4: SIRS extraction using enhanced method from second script
        sirs = extract_sirs_data(s3_uri, structured_tables, provider=llm_provider)
        
        # Convert Pydantic model to dict and add component data
        sirs_dict = sirs.model_dump() if hasattr(sirs, 'model_dump') else sirs.dict()
        sirs_dict['tableData'] = transformed_components
        print(sirs_dict)

        # Step 6: First completely remove the tableData field
        logger.info("Removing tableData field")
        delete_result = coll.update_one(
            {'_id': pid},
            {'$unset': {'tableData': ""}}
        )
        logger.info(f"tableData removal result: {delete_result.modified_count} documents modified")

        # Step 7: Now set the sirsAiResponse and new tableData
        logger.info(f"Setting sirsAiResponse and empty tableData")
        update_result = coll.update_one(
            {'_id': pid}, 
            {
                '$set': {
                    'sirsAiResponse': sirs_dict,
                    'status.SIRSPdfGenAi.status': 2,  # Status 2 for success
                    'tableData': {}  # Set tableData to empty object
                }
            }
        )
        logger.info(f"Update result: {update_result.modified_count} documents modified")

        return {
            "statusCode": 200, 
            "body": json.dumps({
                "message": "Done",
                "components_extracted": len(component_data),
                "confidence": sirs_dict.get("confidence", 0)
            })
        }

    except Exception as e:
        logging.exception("Pipeline error")
        coll.update_one({'_id': pid}, {'$set': {
            'status.SIRSPdfGenAi.status': 5,
            'status.SIRSPdfGenAi.error': str(e),
            'status.pipelineError': str(e)
        }})
        return {"statusCode": 500, "body": json.dumps({"error": str(e)})}