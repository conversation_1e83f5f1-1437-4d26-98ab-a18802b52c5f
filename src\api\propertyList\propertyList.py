import json
import os
import sys
import traceback
from bson.objectid import ObjectId 
from datetime import datetime 

# Add the src directory to the Python path
sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)

from helper.db import connect_db

def convert_datetime(obj):
    """Recursively converts datetime objects to strings in ISO format."""
    if isinstance(obj, dict):
        return {k: convert_datetime(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_datetime(v) for v in obj]
    elif isinstance(obj, datetime):
        return obj.isoformat()  # Convert datetime to string
    return obj

# Hardcoded Building Elements
HARD_CODED_BUILDING_ELEMENTS = [
    {"id": 1, "buildingElement": "Balconies, Concrete, Repairs and Waterproof Coating Applications", "totalLife": "10 to 15"},
    {"id": 2, "buildingElement": "Balconies, Railings, Aluminum, Paint Finishes and Capital Repairs", "totalLife": "6 to 8"},
    {"id": 3, "buildingElement": "Balconies, Railings, Aluminum, Replacement", "totalLife": "40"},
    {"id": 4, "buildingElement": "Door, Front Entrance, Automatic Sliding", "totalLife": "25 years"},
    {"id": 5, "buildingElement": "Roofs, Metal, Mansard", "totalLife": "<50 years"},
    {"id": 6, "buildingElement": "Roof, Modified Bitumen, Coating application", "totalLife": "<10 years"},
    {"id": 7, "buildingElement": "Roof, Modified Bitumen, Replacement", "totalLife": "15 to 20"},
    {"id": 8, "buildingElement": "Roof, Porte-cochere (2024 is Rebuild Cost Over Insurance Proceeds)", "totalLife": "12 to 18"},
    {"id": 9, "buildingElement": "Structural Members, Inspection, Milestone", "totalLife": "<10"},
    {"id": 10, "buildingElement": "Walls, Concrete, Paint Finishes & Repairs (Full Sealant Replacement)", "totalLife": "5 to 7"},
    {"id": 11, "buildingElement": "Windows & Doors, Aluminum Frames, Common", "totalLife": "<50"},
]

# Hardcoded Building Services
HARD_CODED_BUILDING_SERVICES = [
    {"id": 1, "buildingService": "Electrical Systems, Main Panels, Phased", "totalLife": ">70 years"},
    {"id": 2, "buildingService": "Generator, Emergency, 100KW", "totalLife": "<30 years"},
    {"id": 3, "buildingService": "Life Safety System, Control Panel", "totalLife": "<15 years"},
    {"id": 4, "buildingService": "Life Safety System, Emergency Devices", "totalLife": "<25 years"},
    {"id": 5, "buildingService": "Pipes, Riser Sections, Domestic Water, Phased", "totalLife": "<60 years"},
    {"id": 6, "buildingService": "Pipes, Riser Sections, Waste & Vent, Relining, Remaining, Phased", "totalLife": "<60 years"},
]

def merge_building_data(existing_data, hardcoded_data):
    """Merge existing property data with hardcoded data while keeping extra fields."""
    merged_data = []
    existing_dict = {item["id"]: item for item in existing_data}

    for item in hardcoded_data:
        merged_item = {**item, **existing_dict.get(item["id"], {})}
        merged_item.setdefault("lifeRemaining", "")
        merged_item.setdefault("conditionGrade", "")
        merged_data.append(merged_item)

    return merged_data

def fetch_property_data(property_id=None):
    """Fetch property details, including building elements and services."""
    db = connect_db()
    collection = db["PropFromBridge"]

    try:
        if property_id:
            property_filter = {"_id": ObjectId(property_id)}
            existing_property = collection.find_one(property_filter)

            if not existing_property:
                return {"error": "Property not found", "statusCode": 404}

            existing_property["_id"] = str(existing_property["_id"])
            existing_property["buildingElementData"] = merge_building_data(
                existing_property.get("buildingElementData", []), HARD_CODED_BUILDING_ELEMENTS
            )
            existing_property["buildingServicesData"] = merge_building_data(
                existing_property.get("buildingServicesData", []), HARD_CODED_BUILDING_SERVICES
            )

            return convert_datetime(existing_property)  # Ensure JSON serializable

        else:
            properties = list(collection.find({}))
            for item in properties:
                item["_id"] = str(item["_id"])
                item["buildingElementData"] = merge_building_data(
                    item.get("buildingElementData", []), HARD_CODED_BUILDING_ELEMENTS
                )
                item["buildingServicesData"] = merge_building_data(
                    item.get("buildingServicesData", []), HARD_CODED_BUILDING_SERVICES
                )

            return convert_datetime(properties)  # Ensure JSON serializable

    except Exception as e:
        print(traceback.format_exc())
        return {"error": str(e), "statusCode": 500}

def handler(event, context):
    """Lambda function handler to fetch property data."""
    try:
        query_params = event.get("queryStringParameters", {})
        property_id = query_params.get("_id") if query_params else None

        property_data = fetch_property_data(property_id)

        if "error" in property_data:
            return {
                "statusCode": property_data["statusCode"],
                "body": json.dumps({"error": property_data["error"]}),
                "headers": {
                    "Access-Control-Allow-Headers": "Content-Type",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS,GET",
                },
            }

        return {
            "statusCode": 200,
            "body": json.dumps(property_data),
            "headers": {
                "Access-Control-Allow-Headers": "Content-Type",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS,GET",
            },
        }

    except Exception as e:
        print(traceback.format_exc())
        return {"statusCode": 500, "body": json.dumps({"error": str(e)})}
