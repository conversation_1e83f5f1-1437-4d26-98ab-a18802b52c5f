import json
import sys
import os
import logging
import traceback
from bson.objectid import ObjectId  # Required for MongoDB _id filtering
sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)
from helper.db import connect_db

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger()

# Common headers for CORS
common_headers = {
    "Access-Control-Allow-Headers": "Content-Type",
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "OPTIONS,POST",
}

def handler(event, context):
    """Lambda function to update SIRS or Milestone Report Key based on _id"""
    
    # Handle preflight (OPTIONS) request
    if event.get('httpMethod') == 'OPTIONS':
        return {
            "statusCode": 200,
            "body": json.dumps({"message": "CORS preflight successful"}),
            "headers": common_headers,
        }

    try:
        # Connect to MongoDB
        db = connect_db()
        collection = db["PropFromBridge"]
        logger.info("MongoDB connection successful")

        # Parse request body
        body = json.loads(event.get('body', '{}'))

        # Validate required fields
        property_id = body.get("_id")  # Get property ID
        report_key = body.get("reportKey")
        report_type = body.get("type")  # Should be "sirs" or "milestone"

        if not property_id or not report_key or not report_type:
            return {
                "statusCode": 400,
                "body": json.dumps({"error": "Missing required fields: _id, reportKey, type"}),
                "headers": common_headers,
            }

        try:
            object_id = ObjectId(property_id)  # Convert string ID to ObjectId
        except Exception as e:
            logger.error("Invalid property ID format: %s", property_id)
            return {
                "statusCode": 400,
                "body": json.dumps({"error": "Invalid property ID format"}),
                "headers": common_headers,
            }

        # Determine which field to update
        update_field = None
        if report_type == "sirs":
            update_field = {"sirsReportKey": report_key}
        elif report_type == "milestone":
            update_field = {"milestoneReportKey": report_key}
        else:
            return {
                "statusCode": 400,
                "body": json.dumps({"error": "Invalid type. Must be 'sirs' or 'milestone'"}),
                "headers": common_headers,
            }

        # Construct the filter for the property using _id
        property_filter = {"_id": object_id}

        # Update the property in MongoDB
        result = collection.update_one(
            property_filter,
            {'$set': update_field}
        )

        if result.matched_count == 0:
            logger.warning("Property not found for ID: %s", property_id)
            return {
                "statusCode": 404,
                "body": json.dumps({"error": "Property not found"}),
                "headers": common_headers,
            }

        # Log the successful update
        logger.info("Updated %s for property ID %s: %s", report_type, property_id, update_field)

        return {
            "statusCode": 200,
            "body": json.dumps({
                "message": f"{report_type.capitalize()} Report Key updated successfully",
                "status": True
            }),
            "headers": common_headers,
        }

    except Exception as e:
        # Log the error for debugging
        logger.error("Error updating report key: %s", e)
        logger.error("Stack trace: %s", traceback.format_exc())

        return {
            "statusCode": 500,
            "body": json.dumps({"error": "An internal error occurred", "details": str(e)}),
            "headers": common_headers,
        }
