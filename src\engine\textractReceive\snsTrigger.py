import json
import os
import traceback

from pymongo import MongoClient
from bson.objectid import ObjectId
import boto3 

textract =boto3.client('textract')
ssm= boto3.client('ssm')
sqs = boto3.resource('sqs') 
lambda_client = boto3.client('lambda')



# PYTHON_DB_URL=os.environ.get('PYTHON_DB_URL')






def MClient():
        client = MongoClient('mongodb://mongdbuser:%266XR%24a%25jS2W5z%5E%40%24@***********:27017/admin')
        # client = MongoClient("mongodb://localhost:27017")
        return client
    
def remove_dot_from_keys(d): 
    if isinstance(d, dict):
        return {key.replace('.', ''): remove_dot_from_keys(value) for key, value in d.items()}
    elif isinstance(d, list): 
        return [remove_dot_from_keys(item) for item in d]
    else:
        return d
    
def extract_text_from_blocks(blocks):
    extracted_text = ''
    for block in blocks:
        if block['BlockType'] == 'LINE':
            extracted_text += block['Text'] + '\n'
    return extracted_text.strip()

def handler(event, context): 
    client = MClient()
    Property = client['Fasst']
    PropFromBridge = Property['PropFromBridge']
    print(event)
    try:
        for record in event["Records"]:
            
            jobId = json.loads(record["Sns"]["Message"])["JobId"] 
            property=PropFromBridge.find_one({'jobId': jobId,'status.getOcrResultsViaSns.status':0})
            PropFromBridge.update_one({"jobId": jobId}, {"$set": {'status.getOcrResultsViaSns.status':1}})
            response = textract.get_document_text_detection(
                JobId=jobId
            )
            print(response.get("NextToken","No token"))
            print(response)
            blocks = response
            hasNextToken = False
            token = None
            if "NextToken" in response and response["NextToken"]:
                hasNextToken = True
                token = response["NextToken"]
            while (hasNextToken and token):
                resp = textract.get_document_text_detection(
                    JobId=jobId,
                    NextToken=token
                )
                print(resp.get("NextToken","No token")) 
                textBlocks = resp["Blocks"]  
                blocks["Blocks"] = blocks["Blocks"] + textBlocks
                if "NextToken" in resp and resp["NextToken"]:
                    hasNextToken = True
                    token = resp["NextToken"] 
                else:
                    hasNextToken = False
                    token = None
            # textBlocks = blocks.pop("Blocks")
            # blocks["Blocks"] = sorted(
            #     textBlocks, key=lambda d: d["RowIndex"])
            # print(len(blocks["ExpenseDocuments"])) 
            extracted_text = extract_text_from_blocks(blocks['Blocks'])

            PropFromBridge.update_one({"jobId": jobId}, {
                                "$set": {'status.getOcrResultsViaSns.status':2, 'status.SIRSPdfGenAi.status': 0,"sirsReportText":extracted_text}})
            payload = {
            "message": f"Request for ${type} Lambda",
            "data": {'address':property['address']}  # Forward the original event or modify as needed
        }
            target_function=f'arn:aws:lambda:us-east-1:953683027250:function:sirsPdfGenAi-{os.environ["STAGE"]}'
            lambda_client.invoke(
                FunctionName=target_function,
                InvocationType='Event',  # Use 'Event' for async invocation
                Payload=json.dumps(payload))
        
            if 'stopPipelineTrigger' in event and event['stopPipelineTrigger']:
                print("Trigger of next pipeline script disabled")
            else:
                print("success")
                
                
                
    except BaseException:
        PropFromBridge.update_one({"jobId": jobId}, {
                                "$set": {'status.getOcrResultsViaSns.status':3,'status.getOcrResultsViaSns.error':traceback.format_exc()}})
        
        print(traceback.format_exc())
        pass
    return {
        "success": True
    }

# handler({
#   "Records": [
#     {
#       "body": "{\"propertyId\": \"65f02ff05aa89608919d9988\"}"
#     }
#   ]
# },2)